import telebot
import json
import os

ADMIN_ID = 7348312263
path = "/root/clients/Keroles"
REQUIRED_CHANNEL_ID = '-1002807192654' 
BUTTONS_FILE = f'{path}/buttons.json'
user_states = {}
welcome_msg = """
اهلا بالذوق الراقي معك Sb bot
مستعد تغير اللعبه ؟ السوق كبير بس مع sb طريقك واضح 

انا موجود هنا عشان اساعدك وارشدك في عالم sb من خلال عائلة مشروع قبطان
نورتنا وشد الحيل احنا معاك في كل خطوه بكل حب وعطاء
"""
bot = telebot.TeleBot("7826533753:AAEQOVIE-HHSH0iEL02C3lmXBeIsooqMjoE")

def membership_required(func):
    def wrapper(call_or_message):
        if isinstance(call_or_message, telebot.types.CallbackQuery):
            user_id = call_or_message.from_user.id
            chat_id = call_or_message.message.chat.id
        else:
            user_id = call_or_message.from_user.id
            chat_id = call_or_message.chat.id

        try:
            member = bot.get_chat_member(REQUIRED_CHANNEL_ID, user_id)
            if member.status in ['member', 'administrator', 'creator']:
                return func(call_or_message)
            else:
                raise Exception("User is not an active member.")
        except Exception as e:
            bot.send_message(chat_id, "ارجع للكوتش تبعك يعطيك لينك القناه العامه")
                
    return wrapper


def load_buttons_data():
    try:
        with open(BUTTONS_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return {"main_menu": []}

def generate_markup(key='main_menu'):
    markup = telebot.types.InlineKeyboardMarkup(row_width=1)
    buttons_data = load_buttons_data()
    
    keys = key.split('.')
    current_level = buttons_data
    try:
        for k in keys:
            current_level = current_level[k]
    except KeyError:
        return markup

    buttons = []
    for item in current_level:
        text = item.get('text')
        url = item.get('url')
        callback_data = item.get('callback_data')

        if url:
            buttons.append(telebot.types.InlineKeyboardButton(text, url=url))
        elif callback_data:
            buttons.append(telebot.types.InlineKeyboardButton(text, callback_data=callback_data))
    
    markup.add(*buttons)
    return markup

# admin panel
@bot.message_handler(commands=['admin'])
def admin_panel(message):
    if message.from_user.id == ADMIN_ID:
        markup = telebot.types.ReplyKeyboardMarkup(resize_keyboard=True)
        markup.add(
            telebot.types.KeyboardButton('Get JSON File'),
            telebot.types.KeyboardButton('Update JSON File'),
            telebot.types.KeyboardButton('Update Welcome Message')  # ✅ new button
        )
        bot.send_message(message.chat.id, "Welcome to the Admin Panel.", reply_markup=markup)

@bot.message_handler(func=lambda message: message.from_user.id == ADMIN_ID and message.text == 'Get JSON File')
def send_json_file(message):
    if os.path.exists(BUTTONS_FILE):
        with open(BUTTONS_FILE, 'rb') as f:
            bot.send_document(message.chat.id, f, caption="Here is the current JSON file.")
    else:
        bot.send_message(message.chat.id, "The JSON file does not exist yet. Please create one.")

@bot.message_handler(func=lambda message: message.from_user.id == ADMIN_ID and message.text == 'Update JSON File')
def request_json_update(message):
    user_states[message.chat.id] = 'awaiting_json'
    bot.send_message(message.chat.id, "Please send the new `buttons.json` file to update the bot's buttons.")

@bot.message_handler(content_types=['document'], func=lambda message: user_states.get(message.chat.id) == 'awaiting_json')
def update_json_file(message):
    if message.from_user.id != ADMIN_ID:
        return

    try:
        if not message.document.file_name.endswith('.json'):
            bot.reply_to(message, "Invalid file type. Please upload a `.json` file.")
            return

        file_info = bot.get_file(message.document.file_id)
        downloaded_file = bot.download_file(file_info.file_path)

        try:
            json.loads(downloaded_file.decode('utf-8'))
        except json.JSONDecodeError:
            bot.reply_to(message, "Error: The uploaded file is not a valid JSON. Please check the syntax.")
            return

        with open(BUTTONS_FILE, 'wb') as new_file:
            new_file.write(downloaded_file)
        
        bot.reply_to(message, "✅ JSON file updated successfully! The bot's buttons have been changed.")
    except Exception as e:
        bot.reply_to(message, f"An error occurred: {e}")
    finally:
        if message.chat.id in user_states:
            del user_states[message.chat.id]

@bot.message_handler(func=lambda message: message.from_user.id == ADMIN_ID and message.text == 'Update Welcome Message')
def ask_for_new_welcome(message):
    user_states[message.chat.id] = 'awaiting_welcome'
    bot.send_message(message.chat.id, "Please send the new welcome message.")
    
@bot.message_handler(func=lambda message: user_states.get(message.chat.id) == 'awaiting_welcome')
def update_welcome_message(message):
    global welcome_msg
    welcome_msg = message.text
    bot.send_message(message.chat.id, "✅ Welcome message updated successfully!")
    del user_states[message.chat.id]


# user panel
@bot.message_handler(commands=['start', 'help'])
@membership_required
def send_welcome(message):
    global welcome_msg
    markup = generate_markup('main_menu')
    bot.send_message(message.chat.id, welcome_msg, reply_markup=markup)


@bot.callback_query_handler(func=lambda call: True)
@membership_required
def handle_callback_query(call):
    try:
        key = call.data
        markup = generate_markup(key)

        if not markup.keyboard:
            bot.answer_callback_query(call.id, "No further options available here.")
            return
            
        bot.edit_message_text(
            chat_id=call.message.chat.id,
            message_id=call.message.message_id,
            text=f"Menu: {key.replace('_', ' ').title()}",
            reply_markup=markup
        )
        bot.answer_callback_query(call.id)
    except Exception as e:
        print(f"Error in callback handler: {e}")
        bot.answer_callback_query(call.id, "Error processing your request.")


bot.infinity_polling(timeout=10, long_polling_timeout = 5)
